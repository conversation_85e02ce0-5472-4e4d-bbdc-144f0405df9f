name: Deploy Slack Worker to Cloudflare

on:
  push:
    branches:
      - main
    paths:
      - 'workers/slack-agent/**'
  workflow_dispatch:
jobs:
  deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: workers/slack-agent
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm install

      - name: Publish to Cloudflare
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          workingDirectory: 'workers/slack-agent'
          preCommands: |
            echo "*** pre commands ***"
            echo `pwd`
            npx wrangler --version
            npm install wrangler@latest
            echo "******"
