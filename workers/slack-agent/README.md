# Slack Agent - Commuza Content Moderation Bot

A Cloudflare Worker that provides AI-powered content moderation for Slack workspaces using the Commuza platform.

## Features

- **Real-time Message Monitoring**: Automatically scans messages for inappropriate content
- **OpenAI Integration**: Uses OpenAI's moderation API for content analysis
- **Threaded Alerts**: Posts moderation alerts as threaded replies to flagged messages
- **Slash Commands**: Easy setup and status checking via `/activate-moderation` and `/moderation-status`
- **Analytics Storage**: Stores message analytics and flagged content in Supabase
- **Secure**: Proper Slack signature verification and API key validation

## Architecture

- **HTTP-Only**: No Durable Objects, follows simplified Discord agent pattern
- **Event-Driven**: Handles Slack Events API webhooks for message processing
- **Modular**: Clean separation of concerns with dedicated handlers for events and commands

## Setup

### 1. Install Dependencies

```bash
cd workers/slack-agent
npm install
```

### 2. Configure Environment Variables

Create a `.dev.vars` file in the `workers/slack-agent/` directory:

```bash
# Slack Configuration
SLACK_SIGNING_SECRET=your_slack_app_signing_secret
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token

# OpenAI Configuration
OPENAI_API_KEY=sk-your_openai_api_key

# Supabase Configuration
COMMUZA_DB_URL=https://your-project.supabase.co
COMMUZA_DB_KEY=your_supabase_service_role_key

# API Base (configured in wrangler.toml)
COMMUZA_API_BASE=http://localhost:3000/api
```

### 3. Update wrangler.toml

Update the KV namespace ID in `wrangler.toml`:

```toml
[[kv_namespaces]]
binding = "AGENT_CONFIG_KV"
id = "your_actual_kv_namespace_id"
```

### 4. Create Slack App

1. Go to [Slack API](https://api.slack.com/apps) and create a new app
2. Configure **OAuth & Permissions**:
   - Add Bot Token Scopes: `chat:write`, `channels:read`, `groups:read`, `im:read`, `mpim:read`
3. Configure **Event Subscriptions**:
   - Enable Events and set Request URL to: `https://your-worker.your-subdomain.workers.dev/events`
   - Subscribe to Bot Events: `message.channels`, `message.groups`, `message.im`, `message.mpim`
4. Configure **Slash Commands**:
   - `/activate-moderation` → `https://your-worker.your-subdomain.workers.dev/slash`
   - `/moderation-status` → `https://your-worker.your-subdomain.workers.dev/slash`
5. Install the app to your workspace

## Development

### Local Development

```bash
# Start local development server
wrangler dev --local --port 8787

# Or with environment variables
wrangler dev --local --port 8787 \
  --var SLACK_SIGNING_SECRET:your_signing_secret \
  --var SLACK_BOT_TOKEN:xoxb-your-bot-token \
  --var OPENAI_API_KEY:sk-your-openai-key \
  --var COMMUZA_DB_URL:your-supabase-url \
  --var COMMUZA_DB_KEY:your-supabase-key
```

### Testing with ngrok

For local testing with Slack webhooks:

```bash
# Install ngrok
npm install -g ngrok

# Start your worker locally
wrangler dev --local --port 8787

# In another terminal, expose local server
ngrok http 8787

# Use the ngrok URL in your Slack app configuration
# Events: https://abc123.ngrok.io/events
# Slash Commands: https://abc123.ngrok.io/slash
```

### Type Checking

```bash
npm run type-check
```

## Deployment

```bash
# Deploy to Cloudflare Workers
wrangler deploy

# Set production environment variables
wrangler secret put SLACK_SIGNING_SECRET
wrangler secret put SLACK_BOT_TOKEN
wrangler secret put OPENAI_API_KEY
wrangler secret put COMMUZA_DB_URL
wrangler secret put COMMUZA_DB_KEY
```

## Usage

### Activating Moderation

Use the slash command in any channel:

```
/activate-moderation your-commuza-api-key
```

### Checking Status

```
/moderation-status
```

### How It Works

1. **Message Processing**: When a user posts a message, Slack sends an event to `/events`
2. **Content Analysis**: The message is analyzed using OpenAI's moderation API
3. **Alert Generation**: If content is flagged, an alert is posted as a threaded reply
4. **Analytics Storage**: All message data and flagged content is stored in Supabase

## API Endpoints

- `POST /events` - Slack Events API webhook
- `POST /slash` - Slack slash commands webhook

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `SLACK_SIGNING_SECRET` | Slack app signing secret for request verification | Yes |
| `SLACK_BOT_TOKEN` | Bot token starting with `xoxb-` | Yes |
| `OPENAI_API_KEY` | OpenAI API key for content moderation | Yes |
| `COMMUZA_DB_URL` | Supabase project URL | Yes |
| `COMMUZA_DB_KEY` | Supabase service role key | Yes |
| `COMMUZA_API_BASE` | Commuza API base URL | Yes |

## Database Schema

The worker integrates with the existing Commuza database schema:

- `messages` - All processed messages
- `flagged_messages` - Messages that were flagged by moderation

## Security

- **Signature Verification**: All requests are verified using Slack's signing secret
- **API Key Validation**: Commuza API keys are validated before activation
- **Secure Storage**: Sensitive data is stored securely in KV and Supabase

## Troubleshooting

### Common Issues

1. **Invalid Signature**: Check that `SLACK_SIGNING_SECRET` matches your Slack app
2. **Bot Not Responding**: Ensure the bot has proper permissions and is added to channels
3. **Events Not Received**: Verify the Events API URL is correctly configured in Slack
4. **Slash Commands Not Working**: Check the slash command URLs in your Slack app settings

### Debugging

Enable debug logging by setting the `DEBUG` environment variable:

```bash
DEBUG=slack-agent:* wrangler dev --local
```

## Contributing

1. Follow the existing code style and patterns
2. Add tests for new functionality
3. Update documentation for any API changes
4. Ensure proper error handling and logging
