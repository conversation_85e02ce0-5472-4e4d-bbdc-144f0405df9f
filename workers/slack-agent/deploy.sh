#!/bin/bash

# Slack Agent Deployment Script
# This script helps deploy the Slack agent worker to Cloudflare Workers

set -e

echo "🚀 Deploying Slack Agent Worker..."

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI not found. Please install it first:"
    echo "npm install -g wrangler"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "wrangler.toml" ]; then
    echo "❌ wrangler.toml not found. Please run this script from the workers/slack-agent directory."
    exit 1
fi

# Type check
echo "🔍 Running type check..."
npm run type-check

# Deploy the worker
echo "📦 Deploying to Cloudflare Workers..."
wrangler deploy

echo "✅ Deployment complete!"
echo ""
echo "📋 Next steps:"
echo "1. Set up your environment variables using 'wrangler secret put'"
echo "2. Configure your Slack app with the worker URLs"
echo "3. Test the integration with /activate-moderation command"
echo ""
echo "🔗 Worker URL: https://slack-agent.your-subdomain.workers.dev"
echo "📚 See README.md for detailed setup instructions"
