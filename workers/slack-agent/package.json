{"name": "slack-agent", "version": "1.0.0", "description": "Slack bot for content moderation using Commuza API", "main": "src/index.ts", "scripts": {"dev": "wrangler dev --local", "deploy": "wrangler deploy", "test": "vitest", "type-check": "tsc --noEmit"}, "dependencies": {"@sagi.io/workers-slack": "^0.0.58", "@slack/web-api": "^7.0.4"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240512.0", "@types/node": "^20.12.12", "typescript": "^5.4.5", "vitest": "^1.6.0", "wrangler": "^3.57.1"}, "keywords": ["slack", "bot", "moderation", "cloudflare", "workers"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT"}