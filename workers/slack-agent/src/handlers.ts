import {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    SlackRequestWithRespond,
    SlashCommand,
    isPostedMessageEvent,
} from "slack-cloudflare-workers";
import {
    CommuzaKeyValidation,
    Env,
    OpenAIModerationResponse,
    WorkspaceConfig,
    keyFor,
} from "./structured";

// Commuza API key validation
export async function validateCommuzaApiKey(
  commuzaApiKey: string,
  teamId: string,
  env: Env
): Promise<CommuzaKeyValidation> {
  try {
    const response = await fetch(`${env.COMMUZA_API_BASE}/validate-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${commuzaApiKey}`,
      },
      body: JSON.stringify({
        platform: 'slack',
        workspace_id: teamId,
      }),
    });

    if (!response.ok) {
      return {
        valid: false,
        agent_id: '',
        organization_id: '',
        error: `API returned ${response.status}`,
      };
    }

    const data = await response.json() as any;
    return {
      valid: true,
      agent_id: data.agent_id,
      organization_id: data.organization_id,
    };
  } catch (error) {
    console.error('[VALIDATE_KEY] Error:', error);
    return {
      valid: false,
      agent_id: '',
      organization_id: '',
      error: 'Network error',
    };
  }
}

// OpenAI content moderation
export async function moderateContent(text: string, env: Env): Promise<OpenAIModerationResponse> {
  const response = await fetch('https://api.openai.com/v1/moderations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${env.OPENAI_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      input: text,
      model: 'text-moderation-latest',
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  return await response.json();
}

// Store analytics and flagged content in Supabase
export async function storeAnalytics(
  messageData: {
    platform: string;
    workspace_id: string;
    channel_id: string;
    user_id: string;
    message_id: string;
    content: string;
    flagged: boolean;
    categories?: Record<string, boolean>;
    category_scores?: Record<string, number>;
    agent_id: string;
    organization_id: string;
  },
  env: Env
): Promise<void> {
  try {
    // Store in messages table
    const messageResponse = await fetch(`${env.COMMUZA_DB_URL}/rest/v1/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${env.COMMUZA_DB_KEY}`,
        'Content-Type': 'application/json',
        'apikey': env.COMMUZA_DB_KEY,
      },
      body: JSON.stringify({
        platform: messageData.platform,
        workspace_id: messageData.workspace_id,
        channel_id: messageData.channel_id,
        user_id: messageData.user_id,
        message_id: messageData.message_id,
        content: messageData.content,
        agent_id: messageData.agent_id,
        organization_id: messageData.organization_id,
        created_at: new Date().toISOString(),
      }),
    });

    if (!messageResponse.ok) {
      console.error('[SUPABASE] Failed to store message:', await messageResponse.text());
    }

    // Store flagged content if flagged
    if (messageData.flagged) {
      const flaggedResponse = await fetch(`${env.COMMUZA_DB_URL}/rest/v1/flagged_messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${env.COMMUZA_DB_KEY}`,
          'Content-Type': 'application/json',
          'apikey': env.COMMUZA_DB_KEY,
        },
        body: JSON.stringify({
          platform: messageData.platform,
          workspace_id: messageData.workspace_id,
          channel_id: messageData.channel_id,
          user_id: messageData.user_id,
          message_id: messageData.message_id,
          content: messageData.content,
          categories: messageData.categories,
          category_scores: messageData.category_scores,
          agent_id: messageData.agent_id,
          organization_id: messageData.organization_id,
          created_at: new Date().toISOString(),
        }),
      });

      if (!flaggedResponse.ok) {
        console.error('[SUPABASE] Failed to store flagged message:', await flaggedResponse.text());
      }
    }
  } catch (error) {
    console.error('[SUPABASE] Error storing analytics:', error);
  }
}

// Send moderation alert
export async function sendModerationAlert(
  event: any,
  teamId: string,
  moderationResult: any,
  client: any
): Promise<void> {
  try {
    const flaggedCategories = Object.entries(moderationResult.categories)
      .filter(([_, flagged]) => flagged)
      .map(([category, _]) => category);

    const alertText = `🚨 *Content Moderation Alert*\n\n` +
      `*Channel:* <#${event.channel}>\n` +
      `*User:* <@${event.user}>\n` +
      `*Flagged Categories:* ${flaggedCategories.join(', ')}\n` +
      `*Message:* ${event.text?.substring(0, 200)}${event.text && event.text.length > 200 ? '...' : ''}`;

    // Send as threaded reply to the original message
    await client.chat.postMessage({
      channel: event.channel!,
      thread_ts: event.ts,
      text: alertText,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: alertText,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Detected by Commuza AI Moderation • ${new Date().toLocaleString()}`,
            },
          ],
        },
      ],
    });

    console.log(`[ALERT] Sent moderation alert for message in channel ${event.channel}`);
  } catch (error) {
    console.error('[ALERT] Error sending moderation alert:', error);
  }
}

// Process message for moderation
async function processMessage(event: any, teamId: string, env: Env, client: any): Promise<void> {
  try {
    // Get workspace configuration
    const configData = await env.AGENT_CONFIG_KV.get(keyFor(teamId));
    if (!configData) {
      console.log(`[PROCESS_MESSAGE] No configuration found for team ${teamId}`);
      return;
    }

    const config: WorkspaceConfig = JSON.parse(configData);
    if (!config.activated) {
      console.log(`[PROCESS_MESSAGE] Moderation not activated for team ${teamId}`);
      return;
    }

    // Skip if no text content
    if (!event.text || event.text.trim().length === 0) {
      return;
    }

    // Moderate content with OpenAI
    const moderation = await moderateContent(event.text, env);
    const result = moderation.results[0];

    // Store analytics
    await storeAnalytics({
      platform: 'slack',
      workspace_id: teamId,
      channel_id: event.channel || '',
      user_id: event.user || '',
      message_id: event.ts || '',
      content: event.text,
      flagged: result.flagged,
      categories: result.categories,
      category_scores: result.category_scores,
      agent_id: config.agent_id,
      organization_id: config.organization_id,
    }, env);

    // Send alert if content is flagged
    if (result.flagged) {
      await sendModerationAlert(event, teamId, result, client);
    }

  } catch (error) {
    console.error('[PROCESS_MESSAGE] Error:', error);
  }
}

// Message event handler
export const messageHandler: MessageEventHandler<Env> = async (req) => {
  const { payload } = req;
  if (isPostedMessageEvent(payload)) {
    // Only process message events that are not from bots and have text
    if (!('bot_id' in payload) && !payload.subtype && payload.text) {
      const teamId = req.context.teamId || '';
      await processMessage(payload, teamId, req.env, req.context.client);
    }
  }
};

// Activate moderation command acknowledgment
export const activateCommandAck = async (req: SlackRequestWithRespond<Env, SlashCommand>) => {
  // Return empty response to acknowledge quickly
  return "";
};

// Activate moderation command handler
export const activateCommandHandler = async (req: SlackRequestWithRespond<Env, SlashCommand>) => {
  const { payload, context } = req;
  const commuzaApiKey = payload.text.trim();
  const env = req.env;

  if (!commuzaApiKey) {
    await context.respond({
      response_type: 'ephemeral',
      text: '❌ Please provide your Commuza API key.\n\nUsage: `/activate-moderation <your-commuza-api-key>`',
    });
    return;
  }

  try {
    // Validate the API key
    const keyValidation = await validateCommuzaApiKey(commuzaApiKey, payload.team_id, env);

    if (!keyValidation.valid) {
      await context.respond({
        response_type: 'ephemeral',
        text: `❌ Invalid Commuza API key: ${keyValidation.error || 'Unknown error'}\n\nPlease check your API key and try again.`,
      });
      return;
    }

    // Get bot user info
    const authTest = await context.client.auth.test();

    // Store configuration
    const config: WorkspaceConfig = {
      activated: true,
      commuzaApiKey,
      agent_id: keyValidation.agent_id,
      organization_id: keyValidation.organization_id,
      bot_user_id: authTest.user_id as string,
    };

    await env.AGENT_CONFIG_KV.put(keyFor(payload.team_id), JSON.stringify(config));

    await context.respond({
      response_type: 'ephemeral',
      text: '✅ *Moderation Activated!*\n\nCommuza AI moderation is now active in this workspace. The bot will monitor messages and send alerts for inappropriate content.',
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '✅ *Moderation Activated!*\n\nCommuza AI moderation is now active in this workspace.',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*What happens next:*\n• Messages will be automatically scanned for inappropriate content\n• Flagged content will generate threaded alerts\n• Analytics will be stored for your dashboard\n• Use `/moderation-status` to check configuration',
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Agent ID: ${keyValidation.agent_id} • Organization: ${keyValidation.organization_id}`,
            },
          ],
        },
      ],
    });
  } catch (error) {
    console.error('[ACTIVATE_CMD] Error:', error);
    await context.respond({
      response_type: 'ephemeral',
      text: '❌ An error occurred while activating moderation. Please try again later.',
    });
  }
};

// Status command acknowledgment
export const statusCommandAck = async (req: SlackRequestWithRespond<Env, SlashCommand>) => {
  // Return empty response to acknowledge quickly
  return "";
};

// Status command handler
export const statusCommandHandler = async (req: SlackRequestWithRespond<Env, SlashCommand>) => {
  const { payload, context } = req;
  const env = context.env as Env;

  try {
    const configData = await env.AGENT_CONFIG_KV.get(keyFor(payload.team_id));

    if (!configData) {
      await context.respond({
        response_type: 'ephemeral',
        text: '❌ *Moderation Not Configured*\n\nUse `/activate-moderation <your-commuza-api-key>` to set up content moderation for this workspace.',
      });
      return;
    }

    const config: WorkspaceConfig = JSON.parse(configData);

    // Validate current API key
    const keyValidation = await validateCommuzaApiKey(config.commuzaApiKey, payload.team_id, env);

    const statusIcon = config.activated && keyValidation.valid ? '🟢' : '🔴';
    const statusText = config.activated && keyValidation.valid ? 'Active' : 'Inactive';

    let statusDetails = '';
    if (!config.activated) {
      statusDetails = 'Moderation is disabled for this workspace.';
    } else if (!keyValidation.valid) {
      statusDetails = `API key is invalid: ${keyValidation.error || 'Unknown error'}`;
    } else {
      statusDetails = 'All systems operational. Messages are being monitored.';
    }

    await context.respond({
      response_type: 'ephemeral',
      text: `${statusIcon} *Moderation Status: ${statusText}*\n\n${statusDetails}`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${statusIcon} *Moderation Status: ${statusText}*`,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Details:* ${statusDetails}`,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Agent ID: ${config.agent_id} • Organization: ${config.organization_id}`,
            },
          ],
        },
      ],
    });
  } catch (error) {
    console.error('[STATUS_CMD] Error:', error);
    await context.respond({
      response_type: 'ephemeral',
      text: '❌ An error occurred while checking status. Please try again later.',
    });
  }
};
