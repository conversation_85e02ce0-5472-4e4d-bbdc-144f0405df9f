import { SlackAppEnv } from "slack-cloudflare-workers";

// Environment interface extending SlackAppEnv
export interface Env extends SlackAppEnv {
  OPENAI_API_KEY: string;
  COMMUZA_API_BASE: string;
  AGENT_CONFIG_KV: KVNamespace;
  COMMUZA_DB_URL: string;  // Supabase direct URL
  COMMUZA_DB_KEY: string;  // Supabase service role key
}

// Slack Event Types
export interface SlackEvent {
  type: string;
  user?: string;
  text?: string;
  channel?: string;
  ts?: string;
  thread_ts?: string;
  bot_id?: string;
  subtype?: string;
  team?: string;
}

export interface SlackEventPayload {
  token: string;
  team_id: string;
  api_app_id: string;
  event: SlackEvent;
  type: string;
  event_id: string;
  event_time: number;
  authorizations?: Array<{
    enterprise_id?: string;
    team_id: string;
    user_id: string;
    is_bot: boolean;
    is_enterprise_install?: boolean;
  }>;
}

export interface SlackChallenge {
  type: 'url_verification';
  token: string;
  challenge: string;
}

export interface SlackSlashCommand {
  token: string;
  team_id: string;
  team_domain: string;
  channel_id: string;
  channel_name: string;
  user_id: string;
  user_name: string;
  command: string;
  text: string;
  response_url: string;
  trigger_id: string;
}

// Configuration interfaces
export interface WorkspaceConfig {
  activated: boolean;
  commuzaApiKey: string;
  agent_id: string;
  organization_id: string;
  bot_user_id?: string;
}

export interface CommuzaKeyValidation {
  valid: boolean;
  agent_id: string;
  organization_id: string;
  error?: string;
}

export interface OpenAIModerationResponse {
  id: string;
  model: string;
  results: Array<{
    flagged: boolean;
    categories: Record<string, boolean>;
    category_scores: Record<string, number>;
  }>;
}

// Utility functions
export function jsonResponse(data: any, status = 200): Response {
  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': 'application/json' },
  });
}

export function keyFor(teamId: string): string {
  return `slack:${teamId}`;
}
