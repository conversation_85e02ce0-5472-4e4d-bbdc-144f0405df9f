import { SlackApp } from "slack-cloudflare-workers";
import {
  activate<PERSON><PERSON><PERSON><PERSON><PERSON>,
  activate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  messageH<PERSON><PERSON>,
  statusCommandAck,
  statusCommandHandler,
} from "./handlers";
import { Env } from "./structured";

export default {
  async fetch(
    request: Request,
    env: Env,
    ctx: ExecutionContext
  ): Promise<Response> {
    const app = new SlackApp({ env });

    // Register message event handler
    app.anyMessage(messageHandler);

    // Register slash command handlers
    app.command(
      "/activate-moderation",
      activateCommandAck,
      activateCommandHandler
    );

    app.command(
      "/moderation-status",
      statusCommandAck,
      statusCommandHandler
    );

    return await app.run(request, ctx);
  },
};
