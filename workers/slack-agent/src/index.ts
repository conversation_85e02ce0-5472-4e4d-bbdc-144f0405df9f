import {
  SlackA<PERSON>,
  SlackEdgeAppEnv,
  isPostedMessageEvent,
} from "slack-cloudflare-workers";

// Environment interface
interface Env {
  SLACK_SIGNING_SECRET: string;
  SLACK_BOT_TOKEN: string;
  OPENAI_API_KEY: string;
  COMMUZA_API_BASE: string;
  AGENT_CONFIG_KV: KVNamespace;
  COMMUZA_DB_URL: string;  // Supabase direct URL
  COMMUZA_DB_KEY: string;  // Supabase service role key
}

// Slack Event Types
interface SlackEvent {
  type: string;
  user?: string;
  text?: string;
  channel?: string;
  ts?: string;
  thread_ts?: string;
  bot_id?: string;
  subtype?: string;
  team?: string;
}

interface SlackEventPayload {
  token: string;
  team_id: string;
  api_app_id: string;
  event: SlackEvent;
  type: string;
  event_id: string;
  event_time: number;
  authorizations?: Array<{
    enterprise_id?: string;
    team_id: string;
    user_id: string;
    is_bot: boolean;
    is_enterprise_install?: boolean;
  }>;
}

interface SlackChallenge {
  type: 'url_verification';
  token: string;
  challenge: string;
}

interface SlackSlashCommand {
  token: string;
  team_id: string;
  team_domain: string;
  channel_id: string;
  channel_name: string;
  user_id: string;
  user_name: string;
  command: string;
  text: string;
  response_url: string;
  trigger_id: string;
}

// Configuration interfaces
interface WorkspaceConfig {
  activated: boolean;
  commuzaApiKey: string;
  agent_id: string;
  organization_id: string;
  bot_user_id?: string;
}

interface CommuzaKeyValidation {
  valid: boolean;
  agent_id: string;
  organization_id: string;
  error?: string;
}

interface OpenAIModerationResponse {
  id: string;
  model: string;
  results: Array<{
    flagged: boolean;
    categories: Record<string, boolean>;
    category_scores: Record<string, number>;
  }>;
}

// Utility functions
function jsonResponse(data: any, status = 200): Response {
  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': 'application/json' },
  });
}

function keyFor(teamId: string): string {
  return `slack:${teamId}`;
}

// Slack signature verification
async function verifySlackSignature(
  signingSecret: string,
  signature: string,
  timestamp: string,
  body: string
): Promise<boolean> {
  const time = parseInt(timestamp);
  const currentTime = Math.floor(Date.now() / 1000);

  // Check if request is older than 5 minutes
  if (Math.abs(currentTime - time) > 300) {
    return false;
  }

  const baseString = `v0:${timestamp}:${body}`;
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(signingSecret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature_bytes = await crypto.subtle.sign('HMAC', key, encoder.encode(baseString));
  const computed_signature = `v0=${Array.from(new Uint8Array(signature_bytes))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')}`;

  return computed_signature === signature;
}

// Commuza API key validation
async function validateCommuzaApiKey(
  commuzaApiKey: string,
  teamId: string,
  env: Env
): Promise<CommuzaKeyValidation> {
  try {
    const response = await fetch(`${env.COMMUZA_API_BASE}/validate-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${commuzaApiKey}`,
      },
      body: JSON.stringify({
        platform: 'slack',
        workspace_id: teamId,
      }),
    });

    if (!response.ok) {
      return {
        valid: false,
        agent_id: '',
        organization_id: '',
        error: `API returned ${response.status}`,
      };
    }

    const data = await response.json();
    return {
      valid: true,
      agent_id: data.agent_id,
      organization_id: data.organization_id,
    };
  } catch (error) {
    console.error('[VALIDATE_KEY] Error:', error);
    return {
      valid: false,
      agent_id: '',
      organization_id: '',
      error: 'Network error',
    };
  }
}

// OpenAI content moderation
async function moderateContent(text: string, env: Env): Promise<OpenAIModerationResponse> {
  const response = await fetch('https://api.openai.com/v1/moderations', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${env.OPENAI_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      input: text,
      model: 'text-moderation-latest',
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  return await response.json();
}

// Store analytics and flagged content in Supabase
async function storeAnalytics(
  messageData: {
    platform: string;
    workspace_id: string;
    channel_id: string;
    user_id: string;
    message_id: string;
    content: string;
    flagged: boolean;
    categories?: Record<string, boolean>;
    category_scores?: Record<string, number>;
    agent_id: string;
    organization_id: string;
  },
  env: Env
): Promise<void> {
  try {
    // Store in messages table
    const messageResponse = await fetch(`${env.COMMUZA_DB_URL}/rest/v1/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${env.COMMUZA_DB_KEY}`,
        'Content-Type': 'application/json',
        'apikey': env.COMMUZA_DB_KEY,
      },
      body: JSON.stringify({
        platform: messageData.platform,
        workspace_id: messageData.workspace_id,
        channel_id: messageData.channel_id,
        user_id: messageData.user_id,
        message_id: messageData.message_id,
        content: messageData.content,
        agent_id: messageData.agent_id,
        organization_id: messageData.organization_id,
        created_at: new Date().toISOString(),
      }),
    });

    if (!messageResponse.ok) {
      console.error('[SUPABASE] Failed to store message:', await messageResponse.text());
    }

    // Store flagged content if flagged
    if (messageData.flagged) {
      const flaggedResponse = await fetch(`${env.COMMUZA_DB_URL}/rest/v1/flagged_messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${env.COMMUZA_DB_KEY}`,
          'Content-Type': 'application/json',
          'apikey': env.COMMUZA_DB_KEY,
        },
        body: JSON.stringify({
          platform: messageData.platform,
          workspace_id: messageData.workspace_id,
          channel_id: messageData.channel_id,
          user_id: messageData.user_id,
          message_id: messageData.message_id,
          content: messageData.content,
          categories: messageData.categories,
          category_scores: messageData.category_scores,
          agent_id: messageData.agent_id,
          organization_id: messageData.organization_id,
          created_at: new Date().toISOString(),
        }),
      });

      if (!flaggedResponse.ok) {
        console.error('[SUPABASE] Failed to store flagged message:', await flaggedResponse.text());
      }
    }
  } catch (error) {
    console.error('[SUPABASE] Error storing analytics:', error);
  }
}

// Handle Slack Events API
async function handleSlackEvents(body: string, env: Env, ctx: ExecutionContext): Promise<Response> {
  try {
    const payload = JSON.parse(body) as SlackEventPayload | SlackChallenge;

    // Handle URL verification challenge
    if (payload.type === 'url_verification') {
      const challenge = payload as SlackChallenge;
      return new Response(challenge.challenge, {
        headers: { 'Content-Type': 'text/plain' },
      });
    }

    // Handle event callbacks
    if (payload.type === 'event_callback') {
      const eventPayload = payload as SlackEventPayload;
      const event = eventPayload.event;

      // Only process message events
      if (event.type === 'message' && !event.bot_id && !event.subtype && event.text) {
        ctx.waitUntil(processMessage(event, eventPayload.team_id, env));
      }

      return new Response('OK');
    }

    return new Response('Event type not handled');
  } catch (error) {
    console.error('[SLACK_EVENTS] Error:', error);
    return new Response('Internal error', { status: 500 });
  }
}

// Handle slash commands
async function handleSlashCommands(body: string, env: Env): Promise<Response> {
  try {
    const params = new URLSearchParams(body);
    const command: SlackSlashCommand = {
      token: params.get('token') || '',
      team_id: params.get('team_id') || '',
      team_domain: params.get('team_domain') || '',
      channel_id: params.get('channel_id') || '',
      channel_name: params.get('channel_name') || '',
      user_id: params.get('user_id') || '',
      user_name: params.get('user_name') || '',
      command: params.get('command') || '',
      text: params.get('text') || '',
      response_url: params.get('response_url') || '',
      trigger_id: params.get('trigger_id') || '',
    };

    switch (command.command) {
      case '/activate-moderation':
        return handleActivateCommand(command, env);
      case '/moderation-status':
        return handleStatusCommand(command, env);
      default:
        return jsonResponse({
          response_type: 'ephemeral',
          text: 'Unknown command',
        });
    }
  } catch (error) {
    console.error('[SLASH_COMMANDS] Error:', error);
    return jsonResponse({
      response_type: 'ephemeral',
      text: 'An error occurred processing your command.',
    }, 500);
  }
}

// Process message for moderation
async function processMessage(event: SlackEvent, teamId: string, env: Env): Promise<void> {
  try {
    // Get workspace configuration
    const configData = await env.AGENT_CONFIG_KV.get(keyFor(teamId));
    if (!configData) {
      console.log(`[PROCESS_MESSAGE] No configuration found for team ${teamId}`);
      return;
    }

    const config: WorkspaceConfig = JSON.parse(configData);
    if (!config.activated) {
      console.log(`[PROCESS_MESSAGE] Moderation not activated for team ${teamId}`);
      return;
    }

    // Skip if no text content
    if (!event.text || event.text.trim().length === 0) {
      return;
    }

    // Moderate content with OpenAI
    const moderation = await moderateContent(event.text, env);
    const result = moderation.results[0];

    // Store analytics
    await storeAnalytics({
      platform: 'slack',
      workspace_id: teamId,
      channel_id: event.channel || '',
      user_id: event.user || '',
      message_id: event.ts || '',
      content: event.text,
      flagged: result.flagged,
      categories: result.categories,
      category_scores: result.category_scores,
      agent_id: config.agent_id,
      organization_id: config.organization_id,
    }, env);

    // Send alert if content is flagged
    if (result.flagged) {
      await sendModerationAlert(event, teamId, result, env);
    }

  } catch (error) {
    console.error('[PROCESS_MESSAGE] Error:', error);
  }
}

// Send moderation alert
async function sendModerationAlert(
  event: SlackEvent,
  teamId: string,
  moderationResult: any,
  env: Env
): Promise<void> {
  try {
    const web = new SlackRest({botAccessToken: env.SLACK_BOT_TOKEN});

    const flaggedCategories = Object.entries(moderationResult.categories)
      .filter(([_, flagged]) => flagged)
      .map(([category, _]) => category);

    const alertText = `🚨 *Content Moderation Alert*\n\n` +
      `*Channel:* <#${event.channel}>\n` +
      `*User:* <@${event.user}>\n` +
      `*Flagged Categories:* ${flaggedCategories.join(', ')}\n` +
      `*Message:* ${event.text?.substring(0, 200)}${event.text && event.text.length > 200 ? '...' : ''}`;

    // Send as threaded reply to the original message
    await web.chat.postMessage({
      channel: event.channel!,
      thread_ts: event.ts,
      text: alertText,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: alertText,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Detected by Commuza AI Moderation • ${new Date().toLocaleString()}`,
            },
          ],
        },
      ],
    });

    console.log(`[ALERT] Sent moderation alert for message in channel ${event.channel}`);
  } catch (error) {
    console.error('[ALERT] Error sending moderation alert:', error);
  }
}

// Handle activate moderation command
async function handleActivateCommand(command: SlackSlashCommand, env: Env): Promise<Response> {
  const commuzaApiKey = command.text.trim();

  if (!commuzaApiKey) {
    return jsonResponse({
      response_type: 'ephemeral',
      text: '❌ Please provide your Commuza API key.\n\nUsage: `/activate-moderation <your-commuza-api-key>`',
    });
  }

  try {
    // Validate the API key
    const keyValidation = await validateCommuzaApiKey(commuzaApiKey, command.team_id, env);

    if (!keyValidation.valid) {
      return jsonResponse({
        response_type: 'ephemeral',
        text: `❌ Invalid Commuza API key: ${keyValidation.error || 'Unknown error'}\n\nPlease check your API key and try again.`,
      });
    }

    // Get bot user info
    const web = new SlackRest({botAccessToken: env.SLACK_BOT_TOKEN});
    const authTest = await web.auth.test();

    // Store configuration
    const config: WorkspaceConfig = {
      activated: true,
      commuzaApiKey,
      agent_id: keyValidation.agent_id,
      organization_id: keyValidation.organization_id,
      bot_user_id: authTest.user_id as string,
    };

    await env.AGENT_CONFIG_KV.put(keyFor(command.team_id), JSON.stringify(config));

    return jsonResponse({
      response_type: 'ephemeral',
      text: '✅ *Moderation Activated!*\n\nCommuza AI moderation is now active in this workspace. The bot will monitor messages and send alerts for inappropriate content.',
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '✅ *Moderation Activated!*\n\nCommuza AI moderation is now active in this workspace.',
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: '*What happens next:*\n• Messages will be automatically scanned for inappropriate content\n• Flagged content will generate threaded alerts\n• Analytics will be stored for your dashboard\n• Use `/moderation-status` to check configuration',
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Agent ID: ${keyValidation.agent_id} • Organization: ${keyValidation.organization_id}`,
            },
          ],
        },
      ],
    });
  } catch (error) {
    console.error('[ACTIVATE_CMD] Error:', error);
    return jsonResponse({
      response_type: 'ephemeral',
      text: '❌ An error occurred while activating moderation. Please try again later.',
    }, 500);
  }
}

// Handle status command
async function handleStatusCommand(command: SlackSlashCommand, env: Env): Promise<Response> {
  try {
    const configData = await env.AGENT_CONFIG_KV.get(keyFor(command.team_id));

    if (!configData) {
      return jsonResponse({
        response_type: 'ephemeral',
        text: '❌ *Moderation Not Configured*\n\nUse `/activate-moderation <your-commuza-api-key>` to set up content moderation for this workspace.',
      });
    }

    const config: WorkspaceConfig = JSON.parse(configData);

    // Validate current API key
    const keyValidation = await validateCommuzaApiKey(config.commuzaApiKey, command.team_id, env);

    const statusIcon = config.activated && keyValidation.valid ? '🟢' : '🔴';
    const statusText = config.activated && keyValidation.valid ? 'Active' : 'Inactive';

    let statusDetails = '';
    if (!config.activated) {
      statusDetails = 'Moderation is disabled for this workspace.';
    } else if (!keyValidation.valid) {
      statusDetails = `API key is invalid: ${keyValidation.error || 'Unknown error'}`;
    } else {
      statusDetails = 'All systems operational. Messages are being monitored.';
    }

    return jsonResponse({
      response_type: 'ephemeral',
      text: `${statusIcon} *Moderation Status: ${statusText}*\n\n${statusDetails}`,
      blocks: [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `${statusIcon} *Moderation Status: ${statusText}*`,
          },
        },
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text: `*Details:* ${statusDetails}`,
          },
        },
        {
          type: 'context',
          elements: [
            {
              type: 'mrkdwn',
              text: `Agent ID: ${config.agent_id} • Organization: ${config.organization_id}`,
            },
          ],
        },
      ],
    });
  } catch (error) {
    console.error('[STATUS_CMD] Error:', error);
    return jsonResponse({
      response_type: 'ephemeral',
      text: '❌ An error occurred while checking status. Please try again later.',
    }, 500);
  }
}

// Main request handler
export default {
  async fetch(
    request: Request,
    env: Env,
    ctx: ExecutionContext
  ): Promise<Response> {
    if (request.method !== 'POST') {
      return new Response('Method not allowed', { status: 405 });
    }

    const url = new URL(request.url);
    const signature = request.headers.get('X-Slack-Signature');
    const timestamp = request.headers.get('X-Slack-Request-Timestamp');

    if (!signature || !timestamp) {
      return new Response('Missing Slack headers', { status: 400 });
    }

    const body = await request.text();

    // Verify Slack signature
    const isValid = await verifySlackSignature(
      env.SLACK_SIGNING_SECRET,
      signature,
      timestamp,
      body
    );

    if (!isValid) {
      console.error('[SLACK] Invalid signature');
      return new Response('Invalid signature', { status: 401 });
    }

    // Handle different endpoints
    if (url.pathname === '/events') {
      return handleSlackEvents(body, env, ctx);
    } else if (url.pathname === '/slash') {
      return handleSlashCommands(body, env);
    }

    return new Response('Not found', { status: 404 });
  },
};
