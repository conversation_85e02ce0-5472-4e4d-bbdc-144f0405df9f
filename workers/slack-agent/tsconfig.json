{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ES2022", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "skipLibCheck": true, "strict": true, "noEmit": true, "preserveWatchOutput": true, "types": ["@cloudflare/workers-types", "node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}